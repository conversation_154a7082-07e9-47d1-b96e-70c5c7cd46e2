#!/usr/bin/env bun

/**
 * ERC20 Token Distribution API Server Starter
 * 
 * This script starts the Hono-based API server with proper environment loading
 * and error handling for Bun runtime.
 */

// Import centralized configuration and validation
import { validateEnv, validatePrivateKey } from './src/config'

async function startServer() {
  try {
    console.log('🔧 Validating environment...')
    const envCheck = validateEnv()
    if (!envCheck.valid) {
      console.error('❌ Missing required environment variables:')
      envCheck.missing.forEach(key => console.error(`   - ${key}`))
      console.error('\n💡 Please create a .env file based on .env.example')
      process.exit(1)
    }

    validatePrivateKey()

    console.log('🚀 Starting ERC20 Distribution API Server...')

    // Import and start the server
    await import('./src/api-server')
    
  } catch (error) {
    console.error('💥 Failed to start server:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Gracefully shutting down server...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Gracefully shutting down server...')
  process.exit(0)
})

// Start the server
startServer()