#!/usr/bin/env bun

/**
 * ERC20 Token Distribution API Server Starter
 * 
 * This script starts the Hono-based API server with proper environment loading
 * and error handling for Bun runtime.
 */

import { existsSync } from 'fs'

// Load environment variables
const envFiles = ['.env.local', '.env']
for (const envFile of envFiles) {
  if (existsSync(envFile)) {
    console.log(`📄 Loading environment from ${envFile}`)
    break
  }
}

// Validate critical environment variables
function validateEnvironment() {
  const required = ['DISTRIBUTOR_PRIVATE_KEY']
  const missing = required.filter(key => !Bun.env[key] && !process.env[key])
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:')
    missing.forEach(key => console.error(`   - ${key}`))
    console.error('\n💡 Please create a .env file based on .env.example')
    process.exit(1)
  }
}

// Check if private key is placeholder
function validatePrivateKey() {
  const privateKey = Bun.env.DISTRIBUTOR_PRIVATE_KEY || process.env.DISTRIBUTOR_PRIVATE_KEY
  if (privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    console.warn('⚠️  WARNING: Using placeholder private key!')
    console.warn('⚠️  Please set a real private key in your .env file')
    console.warn('⚠️  Distribution will fail with placeholder key')
  }
}

async function startServer() {
  try {
    console.log('🔧 Validating environment...')
    validateEnvironment()
    validatePrivateKey()
    
    console.log('🚀 Starting ERC20 Distribution API Server...')
    
    // Import and start the server
    await import('./api-server')
    
  } catch (error) {
    console.error('💥 Failed to start server:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Gracefully shutting down server...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Gracefully shutting down server...')
  process.exit(0)
})

// Start the server
startServer()