import { readFileSync } from 'fs'

function calculateTotalUsageMaxSum(): void {
  const csvPath = '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv'
  const csvContent = readFileSync(csvPath, 'utf-8')
  const lines = csvContent.trim().split('\n')
  
  // Skip header line
  const dataLines = lines.slice(1)
  
  let totalSum = 0n
  let nonZeroCount = 0
  let totalAddresses = dataLines.length
  
  console.log('Processing CSV data...\n')
  
  for (const line of dataLines) {
    const columns = line.split(',')
    const address = columns[0]
    const allocation = parseFloat(columns[1])
    const totalUsageMax = BigInt(columns[2])
    
    totalSum += totalUsageMax
    
    if (totalUsageMax > 0n) {
      nonZeroCount++
      console.log(`${address}: ${totalUsageMax.toString()} (allocation: ${allocation})`)
    }
  }
  
  console.log('\n=== SUMMARY ===')
  console.log(`Total addresses: ${totalAddresses}`)
  console.log(`Addresses with non-zero totalUsageMax: ${nonZeroCount}`)
  console.log(`Sum of all totalUsageMax values: ${totalSum.toString()}`)
  
  // Convert to more readable format (assuming 18 decimals)
  const totalSumInEther = Number(totalSum) / 1e18
  console.log(`Sum in Ether format (÷ 10^18): ${totalSumInEther.toLocaleString()}`)
  
  // Calculate percentage of addresses with NFTs
  const percentageWithNFTs = (nonZeroCount / totalAddresses * 100).toFixed(2)
  console.log(`Percentage of addresses with NFTs: ${percentageWithNFTs}%`)
}

// Run the calculation
calculateTotalUsageMaxSum()