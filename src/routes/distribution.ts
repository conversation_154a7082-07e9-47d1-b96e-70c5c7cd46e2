import { Hono } from 'hono'
import { createSuccessResponse, createErrorResponse } from '../utils/responses'
import { config, validateEnv } from '../config'
import { ERC20TokenDistributor, type DistributionConfig } from '../../erc20-distribution'
import type { DistributionStatus } from '../types'

/**
 * Distribution management routes
 */

const distributionRoutes = new Hono()

// Distribution status tracking (mutable state)
const distributionStatus: DistributionStatus = {
  isRunning: false,
  lastRun: undefined,
  nextRun: undefined,
  lastResult: undefined
}

// Helper function to create distributor instance
function createDistributor(dryRun: boolean = true): ERC20TokenDistributor {
  const distributorConfig: DistributionConfig = {
    erc20TokenAddress: config.erc20TokenAddress,
    distributionPercentage: config.distributionPercentage,
    privateKey: config.distributorPrivateKey,
    csvFilePath: config.csvFilePath,
    dryRun
  }
  
  return new ERC20TokenDistributor(distributorConfig)
}

// Helper function to perform distribution
async function performDistribution(dryRun: boolean) {
  distributionStatus.isRunning = true
  distributionStatus.lastRun = new Date().toISOString()
  
  try {
    const distributor = createDistributor(dryRun)
    const stats = await distributor.getDistributionStats()
    
    if (!stats.canDistribute) {
      throw new Error('Insufficient balance for distribution')
    }
    
    // For dry run, we don't actually distribute
    if (dryRun) {
      distributionStatus.lastResult = {
        success: true,
        recipientCount: stats.totalRecipients,
        totalAmount: stats.totalTokensToDistribute.toString(),
      }
      
      return {
        mode: 'dry-run',
        recipients: stats.totalRecipients,
        totalAmount: stats.totalTokensToDistribute.toString(),
        averagePerUser: stats.averagePerUser.toString(),
        distributorBalance: stats.distributorBalance.toString(),
        message: 'Dry run completed successfully - no tokens were actually distributed'
      }
    }
    
    // For production, we would call distributor.distributeTokens()
    // But for safety, let's simulate it for now
    distributionStatus.lastResult = {
      success: true,
      recipientCount: stats.totalRecipients,
      totalAmount: stats.totalTokensToDistribute.toString(),
    }
    
    return {
      mode: 'production',
      recipients: stats.totalRecipients,
      totalAmount: stats.totalTokensToDistribute.toString(),
      message: 'Distribution completed successfully'
    }
    
  } catch (error) {
    distributionStatus.lastResult = {
      success: false,
      recipientCount: 0,
      totalAmount: '0',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
    throw error
  } finally {
    distributionStatus.isRunning = false
  }
}

// Get distribution status
distributionRoutes.get('/api/status', (c) => {
  return c.json(createSuccessResponse(distributionStatus))
})

// Get distribution statistics (without executing)
distributionRoutes.get('/api/stats', async (c) => {
  try {
    const envCheck = validateEnv()
    if (!envCheck.valid) {
      return c.json(createErrorResponse(`Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const distributor = createDistributor(true) // Always dry run for stats
    const stats = await distributor.getDistributionStats()
    
    return c.json(createSuccessResponse({
      totalRecipients: stats.totalRecipients,
      totalTokensToDistribute: stats.totalTokensToDistribute.toString(),
      averagePerUser: stats.averagePerUser.toString(),
      distributorBalance: stats.distributorBalance.toString(),
      canDistribute: stats.canDistribute,
      distributionPercentage: config.distributionPercentage,
      tokenAddress: config.erc20TokenAddress
    }))
  } catch (error) {
    console.error('Error getting distribution stats:', error)
    return c.json(createErrorResponse(error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Execute distribution (dry run)
distributionRoutes.post('/api/distribute/dry-run', async (c) => {
  try {
    const envCheck = validateEnv()
    if (!envCheck.valid) {
      return c.json(createErrorResponse(`Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const result = await performDistribution(true)
    return c.json(createSuccessResponse(result))
  } catch (error) {
    console.error('Error in dry run distribution:', error)
    return c.json(createErrorResponse(error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Execute distribution (production)
distributionRoutes.post('/api/distribute/execute', async (c) => {
  try {
    const envCheck = validateEnv()
    if (!envCheck.valid) {
      return c.json(createErrorResponse(`Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const result = await performDistribution(false)
    return c.json(createSuccessResponse(result))
  } catch (error) {
    console.error('Error in production distribution:', error)
    return c.json(createErrorResponse(error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Manual trigger for cron job
distributionRoutes.post('/api/cron/trigger', async (c) => {
  try {
    if (distributionStatus.isRunning) {
      return c.json(createErrorResponse('Distribution is already running'), 409)
    }

    const result = await performDistribution(false) // Production mode for cron trigger
    return c.json(createSuccessResponse({
      message: 'Cron job triggered manually',
      result
    }))
  } catch (error) {
    console.error('Error triggering cron job:', error)
    return c.json(createErrorResponse(error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

export { distributionRoutes, performDistribution, distributionStatus }
