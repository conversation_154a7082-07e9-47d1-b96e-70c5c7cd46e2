import { Hono } from 'hono'
import { serve } from '@hono/node-server'
import * as cron from 'node-cron'

// Import configuration and middleware
import { config, logConfiguration } from './config'
import { allMiddleware } from './middleware'

// Import route modules
import { health } from './routes/health'
import { configRoutes } from './routes/config'
import { distributionRoutes, performDistribution, distributionStatus } from './routes/distribution'
import { historyRoutes } from './routes/history'

/**
 * Simplified Hono.js API Server
 * 
 * This is the main application file that composes all routes and middleware
 * into a single Hono application. The actual route logic is modularized
 * into separate files for better organization.
 */

// Initialize Hono app
const app = new Hono()

// Apply all middleware
allMiddleware.forEach(middleware => {
  app.use('*', middleware)
})

// Register route modules
app.route('/', health)
app.route('/', configRoutes)
app.route('/', distributionRoutes)
app.route('/', historyRoutes)

// Cron job setup
if (config.enableCron) {
  console.log(`⏰ Setting up cron job with schedule: ${config.cronSchedule}`)
  
  cron.schedule(config.cronSchedule, async () => {
    console.log('🕐 Cron job triggered - starting distribution...')
    
    if (distributionStatus.isRunning) {
      console.log('⚠️ Distribution already running, skipping cron execution')
      return
    }
    
    try {
      await performDistribution(false) // Production mode
      console.log('✅ Cron distribution completed successfully')
    } catch (error) {
      console.error('❌ Cron distribution failed:', error)
    }
  })
  
  console.log('✅ Cron job scheduled successfully')
} else {
  console.log('⏸️ Cron job disabled')
}

// Start server
console.log('🚀 Starting ERC20 Distribution API Server...')
logConfiguration()

serve({
  fetch: app.fetch,
  port: config.port,
})

console.log(`🌐 Server running at http://localhost:${config.port}`)
console.log(`📚 API Endpoints:`)
console.log(`   GET  /health                    - Health check`)
console.log(`   GET  /api/config                - Get configuration`)
console.log(`   PUT  /api/config                - Update configuration`)
console.log(`   GET  /api/status                - Get distribution status`)
console.log(`   GET  /api/stats                 - Get distribution statistics`)
console.log(`   POST /api/distribute/dry-run    - Execute dry run`)
console.log(`   POST /api/distribute/execute    - Execute real distribution`)
console.log(`   POST /api/cron/trigger          - Manually trigger distribution`)
console.log(`   GET  /api/history               - Get distribution history`)

export default app
