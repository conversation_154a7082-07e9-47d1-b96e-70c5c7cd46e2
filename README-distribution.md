# ERC20 Token Distribution System

This system distributes ERC20 tokens to users based on their NFT usage data from the CSV file. The distribution amount is calculated as a percentage of each user's `totalUsageMax` value.

## 🚀 Quick Start

### 1. Set up your environment

```bash
# Set your private key (the wallet that will distribute tokens)
export DISTRIBUTOR_PRIVATE_KEY="0x..."

# Install dependencies
bun install
```

### 2. Configure the distribution

Edit `distribute-tokens.ts` to set:
- `erc20TokenAddress`: The ERC20 token contract address
- `distributionPercentage`: Percentage of totalUsageMax to distribute (e.g., 10 = 10%)
- `dryRun`: Set to `true` for testing, `false` for real transactions

### 3. Run distribution

```bash
# Dry run (safe testing)
bun run distribute

# Production run (real transactions)
bun run distribute:prod
```

## 📊 How It Works

1. **Reads CSV Data**: Loads user data from `nft-usage-results.csv`
2. **Calculates Distribution**: For each user with non-zero `totalUsageMax`:
   ```
   tokenAmount = (totalUsageMax × distributionPercentage) / 100
   ```
3. **Validates Balance**: Ensures distributor wallet has sufficient tokens
4. **Distributes Tokens**: Sends ERC20 tokens to each user's wallet

## 📁 Files Overview

- `erc20-distribution.ts`: Core distribution logic and ERC20TokenDistributor class
- `distribute-tokens.ts`: Main script to run token distribution
- `example-usage.ts`: Examples showing different distribution scenarios

## 🔧 Configuration Options

```typescript
interface DistributionConfig {
  erc20TokenAddress: string    // ERC20 token contract address
  distributionPercentage: number // % of totalUsageMax to distribute
  privateKey: string          // Distributor wallet private key
  csvFilePath: string         // Path to CSV file
  dryRun?: boolean           // Test mode (default: true)
}
```

## 📈 Example Distribution

Given this CSV data:
```csv
address,allocation,totalUsageMax
0x123...,9000,142857140000000000000
0x456...,6000,71428568571428571428
```

With `distributionPercentage = 10`:
- User 0x123... receives: `14285714000000000000` tokens (10% of their usage)
- User 0x456... receives: `7142856857142857142` tokens (10% of their usage)

## 🛡️ Safety Features

- **Dry Run Mode**: Test distributions without sending real transactions
- **Balance Validation**: Checks distributor has sufficient tokens before starting
- **Batch Processing**: Processes users in batches to avoid rate limiting
- **Error Handling**: Continues distribution even if individual transfers fail
- **Transaction Logging**: Logs all transaction hashes and results

## 🎯 Usage Examples

### Basic Distribution (5% of usage)
```typescript
const config = {
  erc20TokenAddress: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913', // USDC
  distributionPercentage: 5,
  privateKey: process.env.DISTRIBUTOR_PRIVATE_KEY!,
  csvFilePath: '/path/to/nft-usage-results.csv',
  dryRun: true
}

const distributor = new ERC20TokenDistributor(config)
await distributor.distributeTokens()
```

### Get Distribution Statistics
```typescript
const stats = await distributor.getDistributionStats()
console.log(`Will distribute ${stats.totalTokensToDistribute} tokens to ${stats.totalRecipients} users`)
```

## ⚠️ Important Notes

1. **Private Key Security**: Never commit private keys to version control
2. **Test First**: Always run with `dryRun: true` before production
3. **Token Approval**: Ensure your wallet has sufficient ERC20 token balance
4. **Gas Fees**: Account for gas costs for each transfer transaction
5. **Rate Limiting**: The system includes delays to avoid RPC rate limits

## 🔍 Monitoring

The system provides detailed logging:
- Distribution summary with total recipients and amounts
- Individual transaction hashes
- Success/failure counts
- Token balance validations

## 🚨 Troubleshooting

**"Insufficient balance" error**: 
- Check your wallet has enough tokens to distribute
- Verify the ERC20 token address is correct

**"Transaction failed" errors**:
- Ensure you have enough ETH for gas fees
- Check if recipients' addresses are valid
- Verify the ERC20 contract supports standard transfer function

**Rate limiting issues**:
- The system includes automatic delays between batches
- Consider reducing batch size if needed

## 📞 Support

For issues or questions about the distribution system, check:
1. Console logs for detailed error messages
2. Transaction hashes on block explorer
3. ERC20 token contract documentation