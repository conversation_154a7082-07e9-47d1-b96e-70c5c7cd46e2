import { ERC20TokenDistributor, type DistributionConfig } from './erc20-distribution'

/**
 * Example usage scenarios for the ERC20 token distributor
 */

// Example 1: Basic distribution with 5% of totalUsageMax
async function example1_BasicDistribution() {
  console.log('📝 Example 1: Basic Distribution (5% of usage)')
  
  const config: DistributionConfig = {
    erc20TokenAddress: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913', // USDC on Base
    distributionPercentage: 5, // 5% of totalUsageMax
    privateKey: process.env.DISTRIBUTOR_PRIVATE_KEY!,
    csvFilePath: '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv',
    dryRun: true // Safe mode - no real transactions
  }
  
  const distributor = new ERC20TokenDistributor(config)
  await distributor.distributeTokens()
}

// Example 2: Higher percentage distribution
async function example2_HigherPercentage() {
  console.log('📝 Example 2: Higher Percentage Distribution (15% of usage)')
  
  const config: DistributionConfig = {
    erc20TokenAddress: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913',
    distributionPercentage: 15, // 15% of totalUsageMax
    privateKey: process.env.DISTRIBUTOR_PRIVATE_KEY!,
    csvFilePath: '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv',
    dryRun: true
  }
  
  const distributor = new ERC20TokenDistributor(config)
  
  // Get stats first
  const stats = await distributor.getDistributionStats()
  console.log(`Will distribute ${stats.totalTokensToDistribute} tokens to ${stats.totalRecipients} users`)
  
  await distributor.distributeTokens()
}

// Example 3: Production distribution (real transactions)
async function example3_ProductionDistribution() {
  console.log('📝 Example 3: Production Distribution (REAL TRANSACTIONS)')
  
  const config: DistributionConfig = {
    erc20TokenAddress: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913',
    distributionPercentage: 10, // 10% of totalUsageMax
    privateKey: process.env.DISTRIBUTOR_PRIVATE_KEY!,
    csvFilePath: '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv',
    dryRun: false // ⚠️ REAL TRANSACTIONS!
  }
  
  const distributor = new ERC20TokenDistributor(config)
  
  // Always check stats before production run
  const stats = await distributor.getDistributionStats()
  
  if (!stats.canDistribute) {
    console.error('❌ Cannot distribute - insufficient balance')
    return
  }
  
  console.log('⚠️ WARNING: This will send REAL transactions!')
  console.log(`💸 Total cost: ${stats.totalTokensToDistribute} tokens`)
  console.log('Press Ctrl+C to cancel...')
  
  // Add a delay to allow cancellation
  await new Promise(resolve => setTimeout(resolve, 5000))
  
  await distributor.distributeTokens()
}

// Example 4: Custom token distribution
async function example4_CustomToken() {
  console.log('📝 Example 4: Custom Token Distribution')
  
  const config: DistributionConfig = {
    erc20TokenAddress: '0x4ed4E862860beD51a9570b96d89aF5E1B0Efefed', // Example: DEGEN token
    distributionPercentage: 20, // 20% of totalUsageMax
    privateKey: process.env.DISTRIBUTOR_PRIVATE_KEY!,
    csvFilePath: '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv',
    dryRun: true
  }
  
  const distributor = new ERC20TokenDistributor(config)
  await distributor.distributeTokens()
}

// Run examples
async function runExamples() {
  try {
    // Check if private key is set
    if (!process.env.DISTRIBUTOR_PRIVATE_KEY) {
      console.log('💡 To run examples, set DISTRIBUTOR_PRIVATE_KEY environment variable')
      console.log('💡 Example: export DISTRIBUTOR_PRIVATE_KEY=0x...')
      return
    }
    
    console.log('🚀 Running ERC20 Distribution Examples\n')
    
    // Run dry run examples
    await example1_BasicDistribution()
    console.log('\n' + '='.repeat(60) + '\n')
    
    await example2_HigherPercentage()
    console.log('\n' + '='.repeat(60) + '\n')
    
    await example4_CustomToken()
    console.log('\n' + '='.repeat(60) + '\n')
    
    console.log('✅ All examples completed!')
    console.log('💡 To run production distribution, uncomment example3_ProductionDistribution()')
    
  } catch (error) {
    console.error('❌ Example failed:', error)
  }
}

// Uncomment to run examples
// runExamples()

export {
  example1_BasicDistribution,
  example2_HigherPercentage,
  example3_ProductionDistribution,
  example4_CustomToken
}