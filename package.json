{"name": "vest-subs", "module": "index.ts", "type": "module", "private": true, "devDependencies": {"@biomejs/biome": "^2.1.2", "@types/bun": "latest", "@types/node-cron": "^3.0.11"}, "peerDependencies": {"typescript": "^5"}, "scripts": {"dev": "bun run start-server.ts", "start": "bun run start-server.ts", "api": "bun run start-server.ts", "client": "bun run api-client.ts", "distribute": "bun run distribute-tokens.ts", "distribute:dry": "bun run distribute-tokens.ts", "distribute:prod": "DRY_RUN=false bun run distribute-tokens.ts", "examples": "bun run example-usage.ts", "install:deps": "bun install"}, "dependencies": {"viem": "^2.33.0", "hono": "^4.6.3", "@hono/node-server": "^1.12.2", "node-cron": "^3.0.3"}}