# ERC20 Token Distribution API

A simplified and modular Hono.js-based API server for automated ERC20 token distribution.

## 🚀 Quick Start

### Install Dependencies
```bash
bun install
```

### Environment Setup
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### Start the Server
```bash
# Development mode
bun run dev

# Production mode
bun run start
```

## 🏗️ Simplified Architecture

The project has been refactored into a clean, modular structure:

- **`src/config/`** - Centralized configuration and environment management
- **`src/routes/`** - Modular route handlers organized by functionality
- **`src/middleware/`** - Hono.js middleware configuration
- **`src/types/`** - Shared TypeScript type definitions
- **`src/utils/`** - Utility functions and response helpers

## 📡 API Endpoints

- `GET /health` - Health check
- `GET /api/config` - Get configuration
- `PUT /api/config` - Update configuration
- `GET /api/status` - Get distribution status
- `GET /api/stats` - Get distribution statistics
- `POST /api/distribute/dry-run` - Execute dry run
- `POST /api/distribute/execute` - Execute real distribution
- `POST /api/cron/trigger` - Manually trigger distribution
- `GET /api/history` - Get distribution history

## 🔧 Key Improvements

- **Simplified Structure**: Modular organization for better maintainability
- **Centralized Config**: All environment variables managed in one place
- **Type Safety**: Comprehensive TypeScript types
- **Standardized Responses**: Consistent API response format
- **Error Handling**: Centralized error handling middleware

This project uses [Bun](https://bun.com) runtime and [Hono.js](https://hono.dev) framework for optimal performance.
