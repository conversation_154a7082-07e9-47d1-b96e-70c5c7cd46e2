# ERC20 Token Distribution API

A comprehensive Bun-based API server with Hono.js framework for automated ERC20 token distribution with cron job scheduling.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
bun install
```

### 2. Environment Setup
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 3. Configure Environment Variables
```env
# Required
DISTRIBUTOR_PRIVATE_KEY=0x...your-private-key
ERC20_TOKEN_ADDRESS=0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913

# Optional
DISTRIBUTION_PERCENTAGE=10
PORT=3000
ENABLE_CRON=true
CRON_SCHEDULE=0 0 * * *
```

### 4. Start the API Server
```bash
# Development mode
bun run dev

# Production mode
bun run start
```

## 📡 API Endpoints

### Health & Configuration
- `GET /health` - Server health check
- `GET /api/config` - Get current configuration
- `PUT /api/config` - Update configuration

### Distribution Management
- `GET /api/status` - Get distribution status
- `GET /api/stats` - Get distribution statistics
- `POST /api/distribute/dry-run` - Execute test distribution
- `POST /api/distribute/execute` - Execute real distribution
- `GET /api/history` - Get distribution history

### Cron Management
- `POST /api/cron/trigger` - Manually trigger distribution

## 🕐 Cron Job Configuration

The API includes automatic token distribution via cron jobs:

### Cron Schedule Format
```
# ┌───────────── minute (0 - 59)
# │ ┌───────────── hour (0 - 23)
# │ │ ┌───────────── day of the month (1 - 31)
# │ │ │ ┌───────────── month (1 - 12)
# │ │ │ │ ┌───────────── day of the week (0 - 6) (Sunday to Saturday)
# │ │ │ │ │
# * * * * *
```

### Common Schedules
```env
# Every midnight (default)
CRON_SCHEDULE=0 0 * * *

# Every day at 2 AM
CRON_SCHEDULE=0 2 * * *

# Every Monday at midnight
CRON_SCHEDULE=0 0 * * 1

# Every 6 hours
CRON_SCHEDULE=0 */6 * * *

# Every 30 minutes (for testing)
CRON_SCHEDULE=*/30 * * * *
```

## 🔧 Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `DISTRIBUTOR_PRIVATE_KEY` | ✅ | - | Private key of distributor wallet |
| `ERC20_TOKEN_ADDRESS` | ✅ | USDC Base | ERC20 token contract address |
| `DISTRIBUTION_PERCENTAGE` | ❌ | 10 | Percentage of totalUsageMax to distribute |
| `CSV_FILE_PATH` | ❌ | Auto-detected | Path to CSV file with user data |
| `PORT` | ❌ | 3000 | API server port |
| `ENABLE_CRON` | ❌ | true | Enable/disable cron job |
| `CRON_SCHEDULE` | ❌ | `0 0 * * *` | Cron schedule expression |
| `NODE_ENV` | ❌ | development | Environment mode |

## 📊 API Response Format

All API responses follow this structure:
```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}
```

### Example Success Response
```json
{
  "success": true,
  "data": {
    "totalRecipients": 161,
    "totalTokensToDistribute": "1000000000000000000000",
    "canDistribute": true
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Example Error Response
```json
{
  "success": false,
  "error": "Insufficient balance for distribution",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🔌 Using the API Client

```typescript
import { DistributionApiClient } from './api-client'

const client = new DistributionApiClient('http://localhost:3000')

// Get distribution statistics
const stats = await client.getStats()
console.log('Recipients:', stats.data?.totalRecipients)

// Execute dry run
const dryRun = await client.executeDryRun()
console.log('Dry run result:', dryRun.data)

// Execute real distribution
const result = await client.executeDistribution()
console.log('Distribution result:', result.data)
```

## 🛡️ Security Features

- **Environment Validation**: Checks for required environment variables
- **CORS Protection**: Configurable CORS origins
- **Error Handling**: Comprehensive error handling and logging
- **Dry Run Mode**: Test distributions without real transactions
- **Status Monitoring**: Real-time distribution status tracking

## 📈 Monitoring & Logging

### Health Check
```bash
curl http://localhost:3000/health
```

### Distribution Status
```bash
curl http://localhost:3000/api/status
```

### View Logs
```bash
# Server logs show:
# - Cron job executions
# - Distribution results
# - Error details
# - API requests
```

## 🔄 Distribution Flow

1. **Cron Trigger**: Scheduled job runs at midnight (configurable)
2. **Validation**: Checks environment variables and wallet balance
3. **CSV Processing**: Reads user data from CSV file
4. **Token Calculation**: Calculates distribution amounts based on percentage
5. **Blockchain Execution**: Sends ERC20 tokens to user wallets
6. **Status Update**: Updates distribution status and logs results

## 🧪 Testing

### Test API Endpoints
```bash
# Test client
bun run client

# Manual API testing
curl -X POST http://localhost:3000/api/distribute/dry-run
curl -X GET http://localhost:3000/api/stats
```

### Test Cron Job
```bash
# Trigger manually
curl -X POST http://localhost:3000/api/cron/trigger
```

## 🚨 Troubleshooting

### Common Issues

**Server won't start**
- Check if `.env` file exists and has required variables
- Verify private key format (must start with 0x)
- Ensure port is not already in use

**Distribution fails**
- Check wallet has sufficient token balance
- Verify CSV file path is correct
- Ensure private key has enough ETH for gas fees

**Cron job not running**
- Check `ENABLE_CRON=true` in environment
- Verify cron schedule format
- Check server logs for cron execution

### Debug Mode
```bash
# Enable debug logging
DEBUG=* bun run start
```

## 📦 Production Deployment

### Using PM2
```bash
# Install PM2
bun add -g pm2

# Start with PM2
pm2 start start-server.ts --name "token-distribution-api"

# Monitor
pm2 logs token-distribution-api
pm2 status
```

### Using Docker
```dockerfile
FROM oven/bun:1

WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install

COPY . .
EXPOSE 3000

CMD ["bun", "run", "start"]
```

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3000
ENABLE_CRON=true
CRON_SCHEDULE=0 0 * * *
# Add your production values...
```

## 🔗 Integration Examples

### Webhook Integration
```typescript
// Add webhook notifications to distribution results
app.post('/api/distribute/execute', async (c) => {
  const result = await performDistribution(false)
  
  // Send webhook notification
  await fetch('https://your-webhook-url.com/notify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(result)
  })
  
  return c.json(createResponse(true, result))
})
```

### Database Integration
```typescript
// Add database logging
import { Database } from 'bun:sqlite'

const db = new Database('distributions.db')
db.exec(`
  CREATE TABLE IF NOT EXISTS distributions (
    id INTEGER PRIMARY KEY,
    timestamp TEXT,
    recipients INTEGER,
    tokens_distributed TEXT,
    success BOOLEAN
  )
`)
```

This API provides a complete solution for automated ERC20 token distribution with scheduling, monitoring, and management capabilities.