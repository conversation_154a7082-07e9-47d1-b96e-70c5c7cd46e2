import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { serve } from '@hono/node-server'
import * as cron from 'node-cron'
import { ERC20TokenDistributor, type DistributionConfig } from './erc20-distribution'

// Environment variables with defaults
const PORT = Number(Bun.env.PORT || process.env.PORT || 3000)
const DISTRIBUTOR_PRIVATE_KEY = Bun.env.DISTRIBUTOR_PRIVATE_KEY || process.env.DISTRIBUTOR_PRIVATE_KEY
const ERC20_TOKEN_ADDRESS = Bun.env.ERC20_TOKEN_ADDRESS || process.env.ERC20_TOKEN_ADDRESS || '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913'
const DISTRIBUTION_PERCENTAGE = Number(Bun.env.DISTRIBUTION_PERCENTAGE || process.env.DISTRIBUTION_PERCENTAGE || 10)
const CSV_FILE_PATH = Bun.env.CSV_FILE_PATH || process.env.CSV_FILE_PATH || '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv'
const CRON_SCHEDULE = Bun.env.CRON_SCHEDULE || process.env.CRON_SCHEDULE || '0 0 * * *' // Every midnight
const ENABLE_CRON = (Bun.env.ENABLE_CRON || process.env.ENABLE_CRON || 'true') === 'true'

// Types
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

interface DistributionStatus {
  isRunning: boolean
  lastRun?: string
  nextRun?: string
  lastResult?: {
    success: boolean
    totalRecipients: number
    totalTokensDistributed: string
    successCount: number
    failureCount: number
    error?: string
  }
}

// Global state
let distributionStatus: DistributionStatus = {
  isRunning: false,
  nextRun: ENABLE_CRON ? getNextCronRun() : undefined
}

// Initialize Hono app
const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'], // Add your frontend URLs
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))
app.use('*', prettyJSON())

// Helper functions
function createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
  return {
    success,
    data,
    error,
    timestamp: new Date().toISOString()
  }
}

function getNextCronRun(): string {
  // Simple calculation for next midnight
  const now = new Date()
  const tomorrow = new Date(now)
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(0, 0, 0, 0)
  return tomorrow.toISOString()
}

function validateEnvironment(): { valid: boolean; missing: string[] } {
  const required = ['DISTRIBUTOR_PRIVATE_KEY', 'ERC20_TOKEN_ADDRESS']
  const missing = required.filter(key => !Bun.env[key] && !process.env[key])
  return { valid: missing.length === 0, missing }
}

async function performDistribution(dryRun: boolean = false): Promise<any> {
  if (distributionStatus.isRunning) {
    throw new Error('Distribution is already running')
  }

  distributionStatus.isRunning = true
  
  try {
    const config: DistributionConfig = {
      erc20TokenAddress: ERC20_TOKEN_ADDRESS,
      distributionPercentage: DISTRIBUTION_PERCENTAGE,
      privateKey: DISTRIBUTOR_PRIVATE_KEY!,
      csvFilePath: CSV_FILE_PATH,
      dryRun
    }

    const distributor = new ERC20TokenDistributor(config)
    
    // Get stats first
    const stats = await distributor.getDistributionStats()
    
    if (!dryRun && !stats.canDistribute) {
      throw new Error(`Insufficient balance. Need: ${stats.totalTokensToDistribute}, Have: ${stats.distributorBalance}`)
    }

    // Perform distribution
    await distributor.distributeTokens()
    
    const result = {
      success: true,
      totalRecipients: stats.totalRecipients,
      totalTokensDistributed: stats.totalTokensToDistribute.toString(),
      successCount: stats.totalRecipients, // Simplified for now
      failureCount: 0,
      dryRun
    }

    distributionStatus.lastResult = result
    distributionStatus.lastRun = new Date().toISOString()
    
    return result
  } catch (error) {
    const errorResult = {
      success: false,
      totalRecipients: 0,
      totalTokensDistributed: '0',
      successCount: 0,
      failureCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
      dryRun
    }
    
    distributionStatus.lastResult = errorResult
    distributionStatus.lastRun = new Date().toISOString()
    
    throw error
  } finally {
    distributionStatus.isRunning = false
    if (ENABLE_CRON) {
      distributionStatus.nextRun = getNextCronRun()
    }
  }
}

// Routes

// Health check
app.get('/health', (c) => {
  return c.json(createResponse(true, {
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: Bun.env.NODE_ENV || process.env.NODE_ENV || 'development',
      port: PORT,
      cronEnabled: ENABLE_CRON,
      cronSchedule: CRON_SCHEDULE
    }
  }))
})

// Get distribution configuration
app.get('/api/config', (c) => {
  const envCheck = validateEnvironment()
  
  return c.json(createResponse(true, {
    erc20TokenAddress: ERC20_TOKEN_ADDRESS,
    distributionPercentage: DISTRIBUTION_PERCENTAGE,
    csvFilePath: CSV_FILE_PATH,
    cronSchedule: CRON_SCHEDULE,
    cronEnabled: ENABLE_CRON,
    environmentValid: envCheck.valid,
    missingEnvVars: envCheck.missing
  }))
})

// Get distribution status
app.get('/api/status', (c) => {
  return c.json(createResponse(true, distributionStatus))
})

// Get distribution statistics (without executing)
app.get('/api/stats', async (c) => {
  try {
    const envCheck = validateEnvironment()
    if (!envCheck.valid) {
      return c.json(createResponse(false, null, `Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const config: DistributionConfig = {
      erc20TokenAddress: ERC20_TOKEN_ADDRESS,
      distributionPercentage: DISTRIBUTION_PERCENTAGE,
      privateKey: DISTRIBUTOR_PRIVATE_KEY!,
      csvFilePath: CSV_FILE_PATH,
      dryRun: true
    }

    const distributor = new ERC20TokenDistributor(config)
    const stats = await distributor.getDistributionStats()
    
    return c.json(createResponse(true, {
      totalRecipients: stats.totalRecipients,
      totalTokensToDistribute: stats.totalTokensToDistribute.toString(),
      averagePerUser: stats.averagePerUser.toString(),
      distributorBalance: stats.distributorBalance.toString(),
      canDistribute: stats.canDistribute,
      distributionPercentage: DISTRIBUTION_PERCENTAGE
    }))
  } catch (error) {
    console.error('Error getting stats:', error)
    return c.json(createResponse(false, null, error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Execute distribution (dry run)
app.post('/api/distribute/dry-run', async (c) => {
  try {
    const envCheck = validateEnvironment()
    if (!envCheck.valid) {
      return c.json(createResponse(false, null, `Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const result = await performDistribution(true)
    return c.json(createResponse(true, result))
  } catch (error) {
    console.error('Error in dry run distribution:', error)
    return c.json(createResponse(false, null, error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Execute distribution (production)
app.post('/api/distribute/execute', async (c) => {
  try {
    const envCheck = validateEnvironment()
    if (!envCheck.valid) {
      return c.json(createResponse(false, null, `Missing environment variables: ${envCheck.missing.join(', ')}`), 400)
    }

    const result = await performDistribution(false)
    return c.json(createResponse(true, result))
  } catch (error) {
    console.error('Error in production distribution:', error)
    return c.json(createResponse(false, null, error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Update configuration
app.put('/api/config', async (c) => {
  try {
    const body = await c.req.json()
    
    // Validate input
    if (body.distributionPercentage && (body.distributionPercentage < 0 || body.distributionPercentage > 100)) {
      return c.json(createResponse(false, null, 'Distribution percentage must be between 0 and 100'), 400)
    }

    // Note: In a real application, you might want to persist these changes
    // For now, we'll just return the current config since env vars can't be changed at runtime
    
    return c.json(createResponse(true, {
      message: 'Configuration updated successfully',
      note: 'Environment variables require server restart to take effect',
      currentConfig: {
        erc20TokenAddress: ERC20_TOKEN_ADDRESS,
        distributionPercentage: DISTRIBUTION_PERCENTAGE,
        csvFilePath: CSV_FILE_PATH,
        cronSchedule: CRON_SCHEDULE,
        cronEnabled: ENABLE_CRON
      }
    }))
  } catch (error) {
    console.error('Error updating config:', error)
    return c.json(createResponse(false, null, 'Invalid JSON body'), 400)
  }
})

// Manual trigger for cron job
app.post('/api/cron/trigger', async (c) => {
  try {
    if (distributionStatus.isRunning) {
      return c.json(createResponse(false, null, 'Distribution is already running'), 409)
    }

    // Execute in background
    performDistribution(false).catch(error => {
      console.error('Cron distribution failed:', error)
    })

    return c.json(createResponse(true, {
      message: 'Distribution triggered manually',
      status: 'started'
    }))
  } catch (error) {
    console.error('Error triggering cron:', error)
    return c.json(createResponse(false, null, error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

// Get distribution history/logs (placeholder)
app.get('/api/history', (c) => {
  return c.json(createResponse(true, {
    message: 'Distribution history endpoint',
    lastRun: distributionStatus.lastRun,
    lastResult: distributionStatus.lastResult,
    note: 'Full history logging would be implemented with a database'
  }))
})

// 404 handler
app.notFound((c) => {
  return c.json(createResponse(false, null, 'Endpoint not found'), 404)
})

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err)
  return c.json(createResponse(false, null, 'Internal server error'), 500)
})

// Setup cron job
if (ENABLE_CRON && DISTRIBUTOR_PRIVATE_KEY) {
  console.log(`🕐 Setting up cron job with schedule: ${CRON_SCHEDULE}`)
  
  cron.schedule(CRON_SCHEDULE, async () => {
    console.log('🚀 Cron job triggered - starting token distribution...')
    
    try {
      await performDistribution(false)
      console.log('✅ Cron distribution completed successfully')
    } catch (error) {
      console.error('❌ Cron distribution failed:', error)
    }
  }, {
    scheduled: true,
    timezone: "UTC"
  })
  
  console.log('✅ Cron job scheduled successfully')
} else {
  console.log('⚠️ Cron job disabled or missing private key')
}

// Start server
console.log(`🚀 Starting ERC20 Distribution API Server...`)
console.log(`📊 Configuration:`)
console.log(`   - Port: ${PORT}`)
console.log(`   - Token Address: ${ERC20_TOKEN_ADDRESS}`)
console.log(`   - Distribution %: ${DISTRIBUTION_PERCENTAGE}%`)
console.log(`   - CSV Path: ${CSV_FILE_PATH}`)
console.log(`   - Cron Enabled: ${ENABLE_CRON}`)
console.log(`   - Cron Schedule: ${CRON_SCHEDULE}`)
console.log(`   - Private Key: ${DISTRIBUTOR_PRIVATE_KEY ? '✅ Set' : '❌ Missing'}`)

const server = serve({
  fetch: app.fetch,
  port: PORT,
})

console.log(`🌐 Server running at http://localhost:${PORT}`)
console.log(`📚 API Endpoints:`)
console.log(`   GET  /health                    - Health check`)
console.log(`   GET  /api/config                - Get configuration`)
console.log(`   GET  /api/status                - Get distribution status`)
console.log(`   GET  /api/stats                 - Get distribution statistics`)
console.log(`   POST /api/distribute/dry-run    - Execute dry run`)
console.log(`   POST /api/distribute/execute    - Execute real distribution`)
console.log(`   POST /api/cron/trigger          - Manually trigger distribution`)
console.log(`   GET  /api/history               - Get distribution history`)

export default app