# ERC20 Token Distribution API Configuration

# Server Configuration
PORT=3000
NODE_ENV=development

# Blockchain Configuration
DISTRIBUTOR_PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000
ERC20_TOKEN_ADDRESS=0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913

# Distribution Configuration
DISTRIBUTION_PERCENTAGE=10
CSV_FILE_PATH=/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv

# Cron Job Configuration
ENABLE_CRON=true
CRON_SCHEDULE=0 0 * * *

# Network Configuration (Optional)
RPC_URL=https://base-mainnet.g.alchemy.com/v2/********************************

# Security (Optional)
API_KEY=your-api-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:5173