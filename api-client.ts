/**
 * ERC20 Distribution API Client
 * 
 * TypeScript client for interacting with the distribution API
 */

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

interface DistributionStats {
  totalRecipients: number
  totalTokensToDistribute: string
  averagePerUser: string
  distributorBalance: string
  canDistribute: boolean
  distributionPercentage: number
}

interface DistributionStatus {
  isRunning: boolean
  lastRun?: string
  nextRun?: string
  lastResult?: {
    success: boolean
    totalRecipients: number
    totalTokensDistributed: string
    successCount: number
    failureCount: number
    error?: string
    dryRun?: boolean
  }
}

interface DistributionConfig {
  erc20TokenAddress: string
  distributionPercentage: number
  csvFilePath: string
  cronSchedule: string
  cronEnabled: boolean
  environmentValid: boolean
  missingEnvVars: string[]
}

export class DistributionApiClient {
  private baseUrl: string

  constructor(baseUrl: string = 'http://localhost:3000') {
    this.baseUrl = baseUrl.replace(/\/$/, '') // Remove trailing slash
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Check API health
   */
  async health() {
    return this.request('/health')
  }

  /**
   * Get distribution configuration
   */
  async getConfig(): Promise<ApiResponse<DistributionConfig>> {
    return this.request<DistributionConfig>('/api/config')
  }

  /**
   * Get distribution status
   */
  async getStatus(): Promise<ApiResponse<DistributionStatus>> {
    return this.request<DistributionStatus>('/api/status')
  }

  /**
   * Get distribution statistics
   */
  async getStats(): Promise<ApiResponse<DistributionStats>> {
    return this.request<DistributionStats>('/api/stats')
  }

  /**
   * Execute dry run distribution
   */
  async executeDryRun() {
    return this.request('/api/distribute/dry-run', {
      method: 'POST',
    })
  }

  /**
   * Execute real distribution
   */
  async executeDistribution() {
    return this.request('/api/distribute/execute', {
      method: 'POST',
    })
  }

  /**
   * Manually trigger cron job
   */
  async triggerCron() {
    return this.request('/api/cron/trigger', {
      method: 'POST',
    })
  }

  /**
   * Get distribution history
   */
  async getHistory() {
    return this.request('/api/history')
  }

  /**
   * Update configuration
   */
  async updateConfig(config: Partial<DistributionConfig>) {
    return this.request('/api/config', {
      method: 'PUT',
      body: JSON.stringify(config),
    })
  }
}

// Example usage
export async function exampleUsage() {
  const client = new DistributionApiClient()

  try {
    // Check health
    console.log('🏥 Checking API health...')
    const health = await client.health()
    console.log('Health:', health.data)

    // Get configuration
    console.log('⚙️ Getting configuration...')
    const config = await client.getConfig()
    console.log('Config:', config.data)

    // Get statistics
    console.log('📊 Getting distribution statistics...')
    const stats = await client.getStats()
    console.log('Stats:', stats.data)

    // Get current status
    console.log('📈 Getting distribution status...')
    const status = await client.getStatus()
    console.log('Status:', status.data)

    // Execute dry run
    console.log('🧪 Executing dry run...')
    const dryRun = await client.executeDryRun()
    console.log('Dry run result:', dryRun.data)

    // Note: Uncomment to execute real distribution
    // console.log('🚀 Executing real distribution...')
    // const realDistribution = await client.executeDistribution()
    // console.log('Distribution result:', realDistribution.data)

  } catch (error) {
    console.error('❌ API Error:', error)
  }
}

// CLI usage
if (import.meta.main) {
  console.log('🔌 ERC20 Distribution API Client Example')
  exampleUsage()
}